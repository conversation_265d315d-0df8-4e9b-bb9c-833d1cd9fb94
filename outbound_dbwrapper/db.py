import psycopg2
import os, json
from dotenv import load_dotenv
import select
import requests
import json
from psycopg2.extras import execute_values, execute_batch, RealDictCursor
from collections import defaultdict
from datetime import datetime, timedelta


load_dotenv()


class db_manager:

    def __init__(self):
        
        self.db_params = {
        'dbname': os.getenv('dbname'),
        'user': os.getenv('user'),
        'password': os.getenv('password'),
        'host':os.getenv('host'),
        'port': os.getenv('port')
        }

    def update_number(self,data):
        conn = psycopg2.connect(**self.db_params)
        cursor = conn.cursor()


        try:

            organisation_id = data.get('organisation_id')
            campaign_id = data.get('campaign_id')
            numbers = data.get('numbers')  # List of numbers
            languages = data.get('languages')
            service_dtl = data.get('service_detail')
            mission = data.get('mission')
            

            if not all ([organisation_id,campaign_id,numbers,languages, mission]):
                return "Invalid input"
            print(numbers)

            delete_query = '''DELETE FROM chatbot.registered_campaign where organisation_id = %s and campaign_id = %s;'''  

            cursor.execute(delete_query,(organisation_id,campaign_id))
            conn.commit()

            # insert new rows
            print('Insert new campaign')

            # Prepare data for insertion
            values = [
                (organisation_id, campaign_id, number, languages, service_dtl, mission)
                for number in zip(numbers)
            ]
            print(values)
            insert_query = '''
                INSERT INTO chatbot.registered_campaign (organisation_id, campaign_id, numbers, languages, service_detail, mission)
                VALUES %s;
            '''

            try:
                execute_values(cursor, insert_query, values)
                conn.commit()
                return True
            
            except Exception as e:
                conn.rollback()
                print(f"Error: {e}")
                return 'failure'
                
                
        except Exception as e:
            print('An unexpected error has occured:')
            raise e
        
        finally:
            cursor.close()
            conn.close()


    def register_number(self,data):
        conn = psycopg2.connect(**self.db_params)
        cursor = conn.cursor()

        try:

            organisation_id = data.get('organisation_id')
            campaign_id = data.get('campaign_id')
            numbers = data.get('numbers')  # List of numbers
            languages = data.get('languages')
            service_dtl = data.get('service_detail')
            mission = data.get('mission')
           

            try:
                check_query = '''SELECT campaign_id FROM chatbot.registered_campaign where organisation_id = %s and campaign_id = %s;'''  

                cursor.execute(check_query,(organisation_id,campaign_id))

                campaign = cursor.fetchone()
            except:
                print('An error in check campaign')

            if campaign:
                print(f"The Campaign: {campaign} already exists! Please create a new one or update an existing campaign.")
                return False
            
            else:

                print('Insert new campaign')

                # Prepare data for insertion
                values = [
                    (organisation_id, campaign_id, number, languages, service_dtl, mission)
                    for number in zip(numbers)
                ]
                print(values)
                insert_query = '''
                    INSERT INTO chatbot.registered_campaign (organisation_id, campaign_id, numbers, languages, service_detail, mission)
                    VALUES %s;
                '''

                try:
                    execute_values(cursor, insert_query, values)
                    conn.commit()
                    return True
                
                except Exception as e:
                    conn.rollback()
                    print(f"Error: {e}")
                    return 'failure'
                
        except Exception as e:
            print('An unexpected error has occured:')
            raise e
        
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    def read_campaigns(self,data):

        conn = psycopg2.connect(**self.db_params)
        cursor = conn.cursor()

        organisation_id = data.get('organisation_id')

        try:
            check_query = '''SELECT * FROM chatbot.registered_campaign where organisation_id = %s;'''  

            cursor.execute(check_query,(organisation_id,))

            campaigns = cursor.fetchall()


            grouped_data = defaultdict(lambda: {"organisation_id": "", "campaign_id": "", "languages": set(), "service_detail": "", "mission": "", "numbers": []})

            for org_id, campaign_id, number, language, service_detail, mission in campaigns:
                grouped_data[campaign_id]["organisation_id"] = org_id
                grouped_data[campaign_id]["campaign_id"] = campaign_id
                grouped_data[campaign_id]["languages"].add(language)
                grouped_data[campaign_id]["service_detail"] = service_detail
                grouped_data[campaign_id]["mission"] = mission
                grouped_data[campaign_id]["numbers"].append(number)

            # Convert set of languages to comma-separated string
            output = [{**v, "languages": ", ".join(v["languages"])} for v in grouped_data.values()]


            return output
        except:
            print('An error in check campaign')


    def create_call_schedule(self, data):
        """
        Create a schedule for calling phone numbers within a given calling window and gap.

        Parameters:
        phone_numbers (list): List of phone numbers to call.
        start_date_str (str): The campaign start date in 'YYYY-MM-DD' format.
        window_start_str (str): The daily calling window start time in 'HH:MM' format.
        window_end_str (str): The daily calling window end time in 'HH:MM' format.
        gap_minutes (int): Minimum minutes gap between calls (default is 10).

        Returns:
        list: A list of dictionaries with each phone number and its scheduled call time.
        """
        try:
            to_numbers = data.get('to_numbers')
            from_number = data.get('from_number')
            start_date_str = data.get('start_date_str')
            window_start_str = data.get('window_start_str')
            window_end_str = data.get('window_end_str')
            gap_minutes = data.get('gap_minutes')

            print('start')

            schedule = []
            
            # Parse the campaign start date and the calling window times
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            window_start = datetime.strptime(window_start_str, '%H:%M').time()
            window_end = datetime.strptime(window_end_str, '%H:%M').time()
            
            # Combine the start date with the window start to get the first scheduled call time
            current_datetime = datetime.combine(start_date, window_start)
            # Calculate the window end datetime for the current day
            current_day_window_end = datetime.combine(start_date, window_end)
            
            for phone in to_numbers:
                # If current scheduled time is outside the window, move to the next day
                if current_datetime >= current_day_window_end:
                    # Move to next day, reset current time to that day's window start
                    current_datetime = datetime.combine(current_datetime.date() + timedelta(days=1), window_start)
                    current_day_window_end = datetime.combine(current_datetime.date(), window_end)
                
                schedule.append({
                    'phone_number': phone,
                    'scheduled_time': current_datetime
                })
                
                # Increment current time by the gap for the next call
                current_datetime += timedelta(minutes=gap_minutes)
            for call in schedule:
                print(f"Call {call['phone_number']} from {from_number[0]} at {call['scheduled_time'].strftime('%Y-%m-%d %H:%M')}")
            
            return True, schedule
        except Exception as e:
            print('Error:',e)
            raise e
            schedule = None
            return False, schedule
        
    def book_auto_schedule(self,data):

        organisation_id = data.get('organisation_id')
        campaign_id = data.get('campaign_id')
        from_number = data.get('from_number')  
        to_numbers = data.get('to_numbers')
        start_date = data.get('start_date')
        schedule_times = data.get('schedule_times')
        mark_event = data.get('mark_event')
        
        dt = datetime.strptime(start_date, '%Y-%m-%d')
        booked_on = dt.replace(hour=0, minute=0, second=0)

        
        conn = psycopg2.connect(**self.db_params)
        cursor = conn.cursor()

        if not all ([organisation_id,campaign_id,from_number,to_numbers]):
            print('enter valid information!')
            return False

        mission_id = str(booked_on) + campaign_id + organisation_id

        check_query = '''
                    SELECT * FROM chatbot.call_schedule 
                    WHERE organisation_id=%s AND campaign_id=%s;
                '''
        
        cursor.execute(check_query,(organisation_id,campaign_id))
        result = cursor.fetchone()
        if result:
            print('already booked')
            return False,'already booked'

        print(f'{mission_id} scheduled for {campaign_id}...')
        try:
            if not mark_event:
                mark_event = 'scheduled'
            call_duration = None

            values = [
                    (organisation_id, campaign_id, to_number, from_number, 
                    call_duration, mark_event, booked_on, 
                    schedule_time,mission_id)
                    for to_number, schedule_time in zip(to_numbers,schedule_times)
                ]

            insert_query = '''
                    INSERT INTO chatbot.call_schedule 
                    (organisation_id, campaign_id, number_to, number_from, call_duration, mark_event, 
                    booked_on, schedule_time,mission_id)
                    VALUES %s;
                '''

            execute_values(cursor, insert_query, values)

            conn.commit()
            print('Scheduled')

            return True, mission_id
        except Exception as e:
            print(f'An error in scheduling the call: {e}')
            return False



    def del_campaigns(self,data):

        conn = psycopg2.connect(**self.db_params)
        cursor = conn.cursor()

        organisation_id = data.get('organisation_id')
        campaign_id = data.get('campaign_id')
        

        print(f'{campaign_id} will be deleted...')
        try:
            delete_query = '''DELETE FROM chatbot.registered_campaign where organisation_id = %s and campaign_id = %s;'''  

            cursor.execute(delete_query,(organisation_id,campaign_id))

            conn.commit()

            return True
        except:
            print('An error in delete campaign')
            return False
        

    def book_call(self, data):

        organisation_id = data.get('organisation_id')
        campaign_id = data.get('campaign_id')
        from_number = data.get('number_from')  
        to_numbers = data.get('number_to')
        booked_on = data.get('booked_on')
        schedule_time = data.get('schedule_time')
        mark_event = data.get('mark_event')


        conn = psycopg2.connect(**self.db_params)
        cursor = conn.cursor()

        if not all ([organisation_id,campaign_id,from_number,to_numbers]):
                print('enter valid information!')
                return False
        
        mission_id = booked_on + campaign_id + organisation_id
        check_query = '''
                    SELECT * FROM chatbot.call_schedule 
                    WHERE organisation_id=%s AND campaign_id=%s AND schedule_time=%s;
                '''
        cursor.execute(check_query,(organisation_id,campaign_id,schedule_time))
        result = cursor.fetchone()
        if result:
            print('already booked')
            return 'already booked'

        print(f'{mission_id} scheduled for {campaign_id}...')
        try:
            if not mark_event:
                mark_event = 'scheduled'
            call_duration = None
            
            values = [
                    (organisation_id, campaign_id, to_number, from_number, 
                     call_duration, mark_event, booked_on, 
                     schedule_time,mission_id)
                    for to_number in zip(to_numbers)
                ]
            
            insert_query = '''
                    INSERT INTO chatbot.call_schedule 
                    (organisation_id, campaign_id, number_to, number_from, call_duration, mark_event, 
                    booked_on, schedule_time,mission_id)
                    VALUES %s;
                '''

            execute_values(cursor, insert_query, values)

            conn.commit()
            print('Scheduled')

            return mission_id
        except Exception as e:
            print(f'An error in scheduling the call: {e}')
            return False
        
    def update_call(self, data):

        organisation_id = data.get('organisation_id')
        campaign_id = data.get('campaign_id')
        from_number = data.get('number_from')  
        to_numbers = data.get('number_to')
        booked_on = data.get('booked_on')
        schedule_time = data.get('schedule_time')
        mission_id = data.get("mission_id")

        print(f'Mission_id: {mission_id}')

        conn = psycopg2.connect(**self.db_params)
        cursor = conn.cursor()

        if not all ([organisation_id,campaign_id,from_number,to_numbers]):
                print('enter valid information!')
                return False
        
        print(f'New update time for {campaign_id} now at {schedule_time}...')
        try:

            update_query = '''UPDATE chatbot.call_schedule 
                            SET schedule_time = %s
                            WHERE mission_id = %s;'''
            
            cursor.execute(update_query,(schedule_time,mission_id))
            conn.commit()

            
            print('Updated')

            return True
        except Exception as e:
            print(f'An error in scheduling the call: {e}')
            return False
        
    def delete_call(self, data):

        organisation_id = data.get('organisation_id')
        campaign_id = data.get('campaign_id')
        from_number = data.get('number_from')  
        to_numbers = data.get('number_to')
        booked_on = data.get('booked_on')
        schedule_time = data.get('schedule_time')
        mission_id = data.get("mission_id")

        conn = psycopg2.connect(**self.db_params)
        cursor = conn.cursor()

        if not all ([organisation_id,campaign_id,from_number,to_numbers]):
                print('enter valid information!')
                return False
        
        print(f'Deleting the calls for {campaign_id} which scheduled at {schedule_time}...')
        try:

            delete_query = '''DELETE FROM chatbot.call_schedule 
                            WHERE mission_id = %s and mark_event IN ('scheduled','rescheduled');'''
            
            cursor.execute(delete_query,(mission_id,))
            conn.commit()

            
            print('Deletion successful')

            return True
        except Exception as e:
            print(f'An error in deleting the call: {e}')
            return False
        

    def view_call(self,data):
        
        conn = psycopg2.connect(**self.db_params)
        cursor = conn.cursor()

        organisation_id = data.get('organisation_id')

        try:
            check_query = '''SELECT * FROM chatbot.call_schedule WHERE organisation_id = %s;'''
            cursor.execute(check_query, (organisation_id,))
            schedules = cursor.fetchall()
            
            # Assuming each row is structured as:
            # (organisation_id, campaign_id, number_to, number_from, mark_event,
            #  call_duration, booked_on, schedule_time, booking_id, mission_id)
            output = []
            for row in schedules:
                org_id, campaign_id, number_to, number_from, mark_event, call_duration, booked_on, schedule_time, booking_id, mission_id = row
                
                # If number_to is a list, iterate over its elements.
                if isinstance(number_to, list):
                    for phone in number_to:
                        new_dict = {
                            "organisation_id": org_id,
                            "campaign_id": campaign_id,
                            "number_to": [phone],  # Each dictionary gets a single-element list
                            "number_from": number_from,
                            "mark_event": mark_event,
                            "call_duration": call_duration,
                            "booked_on": booked_on,
                            "schedule_time": schedule_time,
                            "booking_id": booking_id,
                            "mission_id": mission_id
                        }
                        output.append(new_dict)
                else:
                    # If number_to is not a list, wrap it in a list for consistency
                    new_dict = {
                        "organisation_id": org_id,
                        "campaign_id": campaign_id,
                        "number_to": [number_to],
                        "number_from": number_from,
                        "mark_event": mark_event,
                        "call_duration": call_duration,
                        "booked_on": booked_on,
                        "schedule_time": schedule_time,
                        "booking_id": booking_id,
                        "mission_id": mission_id
                    }
                    output.append(new_dict)
            return output
        except Exception as e:
            print(f"Error: {e}")
            return None
        finally:
            cursor.close()
            conn.close()


    def view_bookings(self, data):
            conn = psycopg2.connect(**self.db_params)
            cursor = conn.cursor()
            
            organisation_id = data.get('organisation_id')
            
            try:
                check_query = '''SELECT * FROM chatbot.bookings WHERE organisation_id = %s;'''  
                cursor.execute(check_query, (organisation_id,))
                schedules = cursor.fetchall()  # This will be a list of dictionaries

                grouped_data = defaultdict(lambda: 
                {"organisation_id": "", "campaign_id": "", "number_to": [], "number_from": "",
                "mark_event": "", "call_duration": "", "booked_on":"", "schedule_time":"","booking_id": "","mission_id":""})

                for org_id, campaign_id, number_to, number_from, mark_event, call_duration, booked_on, schedule_time, booking_id, mission_id in schedules:
                    grouped_data[mission_id]["organisation_id"] = org_id
                    grouped_data[mission_id]["campaign_id"] = campaign_id
                    grouped_data[mission_id]["number_to"].append(number_to)
                    grouped_data[mission_id]["number_from"] = number_from
                    grouped_data[mission_id]["mark_event"] = mark_event
                    grouped_data[mission_id]["call_duration"] = call_duration
                    grouped_data[mission_id]["booked_on"] = booked_on
                    grouped_data[mission_id]["schedule_time"] = schedule_time
                    grouped_data[mission_id]["booking_id"] = booking_id
                    grouped_data[mission_id]["mission_id"] = mission_id


                output = [v for v in grouped_data.values()]
                return output
            except Exception as e:
                print(f"Error: {e}")
                return None
            finally:
                cursor.close()
                conn.close()

        
        



