from flask_httpauth import <PERSON><PERSON>P<PERSON>asicAuth
from flask import jsonify,render_template
import os
from flask import Flask,request
import json
from flask_cors import CORS
#.................................................................................................................
from db import db_manager

db = db_manager()

#.................................................................................................................
app = Flask(__name__,template_folder='templates')

CORS(app=app)

@app.route('/', methods=['POST', 'GET'])
def index():
    return "<h1>Service is running</h1>", 200

@app.route('/inbound_status', methods = ['POST','GET'])
def inbound_status():
    if request.method == 'GET':
        call_status = request.args.get('CallStatus')
        duration = request.args.get('Duration')

        print(f"Call Status: {call_status}")
        print(f"Duration: {duration}")
        url = request.args.get('RecordingUrl')
        rduration = request.args.get('RecordingDuration')

        print(f"Recording Url: {url}")
        print(f"Recording Duration: {rduration}")
        return "OK", 200
    elif request.method == 'POST':
        try:
            data = request.get_json()
            print(data)
            return jsonify({"Status":"Success"}), 200
        except Exception as e:
            print(e)
            return jsonify({"Status":"Error"}), 400

@app.route('/recording_callback', methods = ['GET'])
def recording_callback():
    if request.method == 'GET':
        url = request.args.get('RecordingUrl')
        duration = request.args.get('RecordingDuration')

        print(f"Recording Url: {url}")
        print(f"Recording Duration: {duration}")

        return "OK", 200
    


@app.route('/register_number', methods=['POST', 'GET'])
def get_save_request():
    if request.method == 'POST':
        try:  
            data = request.get_json()
            print(data) 
            
            result=db.register_number(data)
            
            if result is True:
                return jsonify({"response":True})
            elif result is False:
                return jsonify({"response":False})

            else:
                return jsonify({"responsce":"an error occured try again later"})
            
        except Exception as e:
            print(f"cannot process because {e}")

@app.route('/auto_schedule', methods=['POST', 'GET'])
def auto_schedule():
    if request.method == 'POST':
        try:  
            data = request.get_json()
            print(data) 
            
            status, schedule = db.create_call_schedule(data)

            if status is True:
                return jsonify({"response":schedule}), 200
            
            elif status is False:
                return jsonify({"response":False}), 400

            else:
                return jsonify({"response":"an error occured try again later"}), 400
            
        except Exception as e:
            print(f"cannot process because {e}")

@app.route('/book_auto_schedule', methods=['POST', 'GET'])
def book_auto_schedule():
    if request.method == 'POST':
        try:  
            data = request.get_json()
            print(data) 
            
            status, result = db.book_auto_schedule(data)

            if status is True:
                return jsonify({"response":result}), 200
            
            elif status is False:
                return jsonify({"response":result}), 400

            else:
                return jsonify({"response":"an error occured try again later"}), 400
            
        except Exception as e:
            print(f"cannot process because {e}")


    

@app.route('/update_number', methods=['POST', 'GET'])
def update_number():
    if request.method == 'POST':
        try:  
            data = request.get_json()
            print(data) 
            
            result=db.update_number(data)
            if result is True:
                return jsonify({"response":True})
            elif result is False:
                return jsonify({"response":False})

            else:
                return jsonify({"responsce":"an error occured try again later"})
            
        except Exception as e:
            print(f"cannot process because {e}")


@app.route('/view_campaigns', methods=['POST', 'GET'])
def get_campaigns():
    if request.method == 'POST':
        try:  
            data = request.get_json()
            print(data) 
            
            result=db.read_campaigns(data)

            if result:
                return jsonify({"response":result})

            else:
                return jsonify({"response":"an error occured try again later"})
            
        except Exception as e:
            print(f"cannot process because {e}")

@app.route('/delete_campaign', methods=['POST','GET'])
def delete():
    if request.method == 'POST':
        try:  
            data = request.get_json()
            print(data) 
            organisation_id = data.get('organisation_id')
            if not organisation_id:
                return jsonify({"error": False}), 400
            
            campaign_id = data.get('campaign_id')
            if not campaign_id:
                return jsonify({"error": False}), 400
            
            result=db.del_campaigns(data)

            if result is True:
                return jsonify({"response":True}), 200
            elif result is False:
                return jsonify({"response":False}), 200

            else:
                return jsonify({"responsce":"an error occured try again later"}), 400
            
        except Exception as e:
            print(f"cannot process because {e}")

@app.route('/call_schedule', methods=['POST', 'GET'])
def call_schedule():
    if request.method == 'POST':
        try:  
            data = request.get_json()
            print(data) 
            
            result=db.book_call(data)

            if result:
                return jsonify({"response":result}), 200
            elif result is False:
                return jsonify({"response":False}), 400

            else:
                return jsonify({"responsce":"an error occured try again later"}), 400
            
        except Exception as e:
            print(f"cannot process because {e}")


@app.route('/update_schedule', methods=['POST', 'GET'])
def update_schedule():
    if request.method == 'POST':
        try:  
            data = request.get_json()
            print(data) 
            
            result=db.update_call(data)

            if result is True:
                return jsonify({"response":True}), 200
            elif result is False:
                return jsonify({"response":False}), 400

            else:
                return jsonify({"responsce":"an error occured try again later"}), 400
            
        except Exception as e:
            print(f"cannot process because {e}")

@app.route('/delete_schedule', methods=['POST', 'GET'])
def delete_schedule():
    if request.method == 'POST':
        try:  
            data = request.get_json()
            print(data) 
            
            result=db.delete_call(data)

            if result is True:
                return jsonify({"response":True}), 200
            elif result is False:
                return jsonify({"response":False}), 400

            else:
                return jsonify({"responsce":"an error occured try again later"}), 400
            
        except Exception as e:
            print(f"cannot process because {e}")


@app.route('/view_schedule', methods=['POST', 'GET'])
def view_schedule():
    if request.method == 'POST':
        try:  
            data = request.get_json()
            print(data) 
            
            result=db.view_call(data)

            if result:
                return jsonify({"response":result}), 200

            else:
                return jsonify({"response":"an error occured try again later"}), 400
            
        except Exception as e:
            print(f"cannot process because {e}")

@app.route('/view_bookings', methods=['POST', 'GET'])
def view_bookings():
    if request.method == 'POST':
        try:  
            data = request.get_json()
            print(data) 
            
            result=db.view_bookings(data)

            if result:
                return jsonify({"response":result}), 200

            else:
                return jsonify({"response":"an error occured try again later"}), 400
            
        except Exception as e:
            print(f"cannot process because {e}")






#.........................................................................................................................
if __name__ == '__main__':
    app.run(debug=False,port=7008,host='0.0.0.0')