# import requests, aiohttp, time
import json
import asyncio
import websockets
from fastapi import FastAPI, WebSocket, Request, Depends, HTTPException, WebSocketException
from fastapi.responses import HTMLResponse
from fastapi.websockets import WebSocketDisconnect
from twilio.twiml.voice_response import VoiceResponse, Connect, Stream, Parameter, Say
from twilio.rest import Client
from dotenv import load_dotenv
from urllib.parse import parse_qs

from datetime import datetime
import math

import logging
import base64
import os


logger = logging.getLogger(__name__)

# --- FastAPI Application Instance ---
app = FastAPI()

from fastapi import FastAPI, WebSocket
import asyncio
import numpy as np
import wave
import datetime

app = FastAPI()

# Constants
SAMPLE_RATE = 8000               # 8kHz
CHUNK_DURATION_SEC = 1           # Process every 1 second
BYTES_PER_SAMPLE = 2             # PCM16 = 2 bytes per sample
CHUNK_SIZE = SAMPLE_RATE * BYTES_PER_SAMPLE * CHUNK_DURATION_SEC  # 16000 bytes

# Queue for async processing
audio_queue = asyncio.Queue()

# Optional: Save to file for debugging
def save_to_wav(pcm_bytes, sample_rate=SAMPLE_RATE, file_prefix="audio_chunk"):
    timestamp = datetime.datetime.utcnow().strftime("%Y%m%d_%H%M%S%f")
    filename = f"{file_prefix}_{timestamp}.wav"
    with wave.open(filename, 'wb') as wf:
        wf.setnchannels(1)            # Mono
        wf.setsampwidth(2)            # 16-bit
        wf.setframerate(sample_rate)
        wf.writeframes(pcm_bytes)
    print(f"Saved: {filename}")

# Async consumer that processes audio chunks
async def audio_consumer():
    buffer = b""
    while True:
        audio_bytes = await audio_queue.get()
        buffer += audio_bytes

        # If buffer large enough, send to model
        if len(buffer) >= CHUNK_SIZE:
            chunk = buffer[:CHUNK_SIZE]
            buffer = buffer[CHUNK_SIZE:]

            # Optional: Save for verification
            save_to_wav(chunk)

            # Call your inference model here (OpenAI, Whisper, etc.)
            # Example: result = await openai_streaming_model(chunk)
            print("Processed 1 second of audio")

# Configuration
WEBSOCKET_HOST = '0.0.0.0'
WEBSOCKET_PORT = 8088 # Change this to your desired port
OUTPUT_FILE = 'audio.raw'

# Ensure output file is fresh
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

@app.get("/")
async def index_page():
    return {"message": "Twilio Media Stream Server is running!"}


@app.websocket("/rtp-stream")
async def audio_handler(websocket, path):
    """
    Handler for incoming WebSocket connections.
    Receives binary audio frames and writes them to a raw file.
    """
    print(f"Client connected: {websocket.remote_address}")
    try:
        async for message in websocket:
            # websockets library delivers `bytes` for binary frames
            if isinstance(message, bytes):
                print(f"Received {len(message)} bytes of audio")
                with open(OUTPUT_FILE, 'ab') as f:
                    f.write(message)
            else:
                # Optionally handle text frames
                print(f"Ignoring text frame: {message}")
    except websockets.exceptions.ConnectionClosed as e:
        print(f"Connection closed: {e}")

async def main():
    print(f"Starting WebSocket server on ws://{WEBSOCKET_HOST}:{WEBSOCKET_PORT}")
    async with websockets.serve(audio_handler, WEBSOCKET_HOST, WEBSOCKET_PORT):
        await asyncio.Future()  # run forever

if __name__ == "__main__":
    asyncio.run(main())